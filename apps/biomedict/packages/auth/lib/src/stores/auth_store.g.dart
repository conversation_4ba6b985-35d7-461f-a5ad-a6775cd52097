// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AuthStore on _AuthStore, Store {
  Computed<bool>? _$isAuthenticatedComputed;

  @override
  bool get isAuthenticated =>
      (_$isAuthenticatedComputed ??= Computed<bool>(
            () => super.isAuthenticated,
            name: '_AuthStore.isAuthenticated',
          ))
          .value;
  Computed<bool>? _$isUnauthenticatedComputed;

  @override
  bool get isUnauthenticated =>
      (_$isUnauthenticatedComputed ??= Computed<bool>(
            () => super.isUnauthenticated,
            name: '_AuthStore.isUnauthenticated',
          ))
          .value;
  Computed<bool>? _$hasErrorComputed;

  @override
  bool get hasError =>
      (_$hasErrorComputed ??= Computed<bool>(
            () => super.hasError,
            name: '_AuthStore.hasError',
          ))
          .value;
  Computed<bool>? _$isEmailVerifiedComputed;

  @override
  bool get isEmailVerified =>
      (_$isEmailVerifiedComputed ??= Computed<bool>(
            () => super.isEmailVerified,
            name: '_AuthStore.isEmailVerified',
          ))
          .value;
  Computed<String>? _$displayNameComputed;

  @override
  String get displayName =>
      (_$displayNameComputed ??= Computed<String>(
            () => super.displayName,
            name: '_AuthStore.displayName',
          ))
          .value;

  late final _$userAtom = Atom(name: '_AuthStore.user', context: context);

  @override
  UserModel? get user {
    _$userAtom.reportRead();
    return super.user;
  }

  @override
  set user(UserModel? value) {
    _$userAtom.reportWrite(value, super.user, () {
      super.user = value;
    });
  }

  late final _$authStateAtom = Atom(
    name: '_AuthStore.authState',
    context: context,
  );

  @override
  AuthState get authState {
    _$authStateAtom.reportRead();
    return super.authState;
  }

  @override
  set authState(AuthState value) {
    _$authStateAtom.reportWrite(value, super.authState, () {
      super.authState = value;
    });
  }

  late final _$isLoadingAtom = Atom(
    name: '_AuthStore.isLoading',
    context: context,
  );

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$errorMessageAtom = Atom(
    name: '_AuthStore.errorMessage',
    context: context,
  );

  @override
  String? get errorMessage {
    _$errorMessageAtom.reportRead();
    return super.errorMessage;
  }

  @override
  set errorMessage(String? value) {
    _$errorMessageAtom.reportWrite(value, super.errorMessage, () {
      super.errorMessage = value;
    });
  }

  late final _$successMessageAtom = Atom(
    name: '_AuthStore.successMessage',
    context: context,
  );

  @override
  String? get successMessage {
    _$successMessageAtom.reportRead();
    return super.successMessage;
  }

  @override
  set successMessage(String? value) {
    _$successMessageAtom.reportWrite(value, super.successMessage, () {
      super.successMessage = value;
    });
  }

  late final _$signInWithEmailAndPasswordAsyncAction = AsyncAction(
    '_AuthStore.signInWithEmailAndPassword',
    context: context,
  );

  @override
  Future<bool> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) {
    return _$signInWithEmailAndPasswordAsyncAction.run(
      () => super.signInWithEmailAndPassword(email: email, password: password),
    );
  }

  late final _$createUserWithEmailAndPasswordAsyncAction = AsyncAction(
    '_AuthStore.createUserWithEmailAndPassword',
    context: context,
  );

  @override
  Future<bool> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) {
    return _$createUserWithEmailAndPasswordAsyncAction.run(
      () => super.createUserWithEmailAndPassword(
        email: email,
        password: password,
      ),
    );
  }

  late final _$signOutAsyncAction = AsyncAction(
    '_AuthStore.signOut',
    context: context,
  );

  @override
  Future<bool> signOut() {
    return _$signOutAsyncAction.run(() => super.signOut());
  }

  late final _$sendPasswordResetEmailAsyncAction = AsyncAction(
    '_AuthStore.sendPasswordResetEmail',
    context: context,
  );

  @override
  Future<bool> sendPasswordResetEmail({required String email}) {
    return _$sendPasswordResetEmailAsyncAction.run(
      () => super.sendPasswordResetEmail(email: email),
    );
  }

  late final _$sendEmailVerificationAsyncAction = AsyncAction(
    '_AuthStore.sendEmailVerification',
    context: context,
  );

  @override
  Future<bool> sendEmailVerification() {
    return _$sendEmailVerificationAsyncAction.run(
      () => super.sendEmailVerification(),
    );
  }

  late final _$reloadUserAsyncAction = AsyncAction(
    '_AuthStore.reloadUser',
    context: context,
  );

  @override
  Future<bool> reloadUser() {
    return _$reloadUserAsyncAction.run(() => super.reloadUser());
  }

  late final _$updateDisplayNameAsyncAction = AsyncAction(
    '_AuthStore.updateDisplayName',
    context: context,
  );

  @override
  Future<bool> updateDisplayName(String displayName) {
    return _$updateDisplayNameAsyncAction.run(
      () => super.updateDisplayName(displayName),
    );
  }

  late final _$deleteAccountAsyncAction = AsyncAction(
    '_AuthStore.deleteAccount',
    context: context,
  );

  @override
  Future<bool> deleteAccount() {
    return _$deleteAccountAsyncAction.run(() => super.deleteAccount());
  }

  late final _$_AuthStoreActionController = ActionController(
    name: '_AuthStore',
    context: context,
  );

  @override
  void _initializeAuthListener() {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._initializeAuthListener',
    );
    try {
      return super._initializeAuthListener();
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setLoading(bool loading) {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._setLoading',
    );
    try {
      return super._setLoading(loading);
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setError(String? error) {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._setError',
    );
    try {
      return super._setError(error);
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setSuccess(String? message) {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._setSuccess',
    );
    try {
      return super._setSuccess(message);
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearMessages() {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore.clearMessages',
    );
    try {
      return super.clearMessages();
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
user: ${user},
authState: ${authState},
isLoading: ${isLoading},
errorMessage: ${errorMessage},
successMessage: ${successMessage},
isAuthenticated: ${isAuthenticated},
isUnauthenticated: ${isUnauthenticated},
hasError: ${hasError},
isEmailVerified: ${isEmailVerified},
displayName: ${displayName}
    ''';
  }
}
