import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart' as ui_auth;
import '../di/auth_di.dart';
import '../models/auth_state.dart';

/// Main authentication wrapper that handles the auth flow
class AuthWrapper extends StatelessWidget {
  final Widget Function(BuildContext context) authenticatedBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final List<ui_auth.AuthProvider> authProviders;

  const AuthWrapper({
    super.key,
    required this.authenticatedBuilder,
    this.loadingBuilder,
    this.authProviders = const [],
  });

  @override
  Widget build(BuildContext context) {
    final authStore = AuthDI.authStore;

    return Observer(
      builder: (context) {
        // Show loading if auth state is being determined
        if (authStore.authState == AuthState.loading) {
          return loadingBuilder?.call(context) ?? const _DefaultLoadingWidget();
        }

        // Show authenticated content if user is signed in
        if (authStore.isAuthenticated) {
          return authenticatedBuilder(context);
        }

        // Show authentication UI
        return ui_auth.SignInScreen(
          providers: authProviders.isNotEmpty ? authProviders : [ui_auth.EmailAuthProvider()],
          headerBuilder: (context, constraints, shrinkOffset) {
            return const _AuthHeader();
          },
          sideBuilder: (context, constraints) {
            return const _AuthSideContent();
          },
          footerBuilder: (context, constraints) {
            return const _AuthFooter();
          },
          actions: [
            ui_auth.AuthStateChangeAction<ui_auth.SignedIn>((context, state) {
              // Handle successful sign in
              authStore.clearMessages();
            }),
            ui_auth.AuthStateChangeAction<ui_auth.UserCreated>((context, state) {
              // Handle successful user creation
              authStore.clearMessages();
            }),
            ui_auth.AuthStateChangeAction<ui_auth.AuthFailed>((context, state) {
              // Handle auth failure
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.exception.toString()),
                  backgroundColor: Colors.red,
                ),
              );
            }),
          ],
        );
      },
    );
  }
}

/// Default loading widget
class _DefaultLoadingWidget extends StatelessWidget {
  const _DefaultLoadingWidget();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading...'),
          ],
        ),
      ),
    );
  }
}

/// Header widget for auth screens
class _AuthHeader extends StatelessWidget {
  const _AuthHeader();

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(24.0),
      child: Column(
        children: [
          Icon(
            Icons.medical_services,
            size: 80,
            color: Colors.deepPurple,
          ),
          SizedBox(height: 16),
          Text(
            'BiomeDict',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.deepPurple,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Your Medical Dictionary',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

/// Side content for auth screens (used in larger screens)
class _AuthSideContent extends StatelessWidget {
  const _AuthSideContent();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.deepPurple.shade50,
      child: const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.security,
                size: 120,
                color: Colors.deepPurple,
              ),
              SizedBox(height: 24),
              Text(
                'Secure Authentication',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Your data is protected with industry-standard security measures.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Footer widget for auth screens
class _AuthFooter extends StatelessWidget {
  const _AuthFooter();

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        '© 2024 BiomeDict. All rights reserved.',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey,
        ),
      ),
    );
  }
}
