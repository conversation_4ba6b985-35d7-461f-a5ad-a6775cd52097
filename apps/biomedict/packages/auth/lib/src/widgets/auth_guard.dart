import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import '../di/auth_di.dart';

/// Widget that guards content based on authentication state
class AuthGuard extends StatelessWidget {
  final Widget child;
  final Widget Function(BuildContext context)? fallback;
  final bool requireEmailVerification;
  final VoidCallback? onUnauthenticated;

  const AuthGuard({
    super.key,
    required this.child,
    this.fallback,
    this.requireEmailVerification = false,
    this.onUnauthenticated,
  });

  @override
  Widget build(BuildContext context) {
    final authStore = AuthDI.authStore;

    return Observer(
      builder: (context) {
        // Check if user is authenticated
        if (!authStore.isAuthenticated) {
          onUnauthenticated?.call();
          return fallback?.call(context) ?? const _UnauthenticatedWidget();
        }

        // Check email verification if required
        if (requireEmailVerification && !authStore.isEmailVerified) {
          return const _EmailVerificationRequiredWidget();
        }

        // User is authenticated and meets all requirements
        return child;
      },
    );
  }
}

/// Widget shown when user is not authenticated
class _UnauthenticatedWidget extends StatelessWidget {
  const _UnauthenticatedWidget();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Authentication Required',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Please sign in to access this content.',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget shown when email verification is required
class _EmailVerificationRequiredWidget extends StatelessWidget {
  const _EmailVerificationRequiredWidget();

  @override
  Widget build(BuildContext context) {
    final authStore = AuthDI.authStore;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Verification Required'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => authStore.signOut(),
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: Observer(
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.mark_email_unread,
                  size: 80,
                  color: Colors.orange,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Verify Your Email',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Please verify your email address (${authStore.user?.email}) to continue.',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 32),
                
                // Show success/error messages
                if (authStore.successMessage != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      border: Border.all(color: Colors.green.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      authStore.successMessage!,
                      style: TextStyle(color: Colors.green.shade700),
                    ),
                  ),
                
                if (authStore.errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      border: Border.all(color: Colors.red.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      authStore.errorMessage!,
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  ),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: authStore.isLoading ? null : () => authStore.sendEmailVerification(),
                      icon: authStore.isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.email),
                      label: const Text('Resend Email'),
                    ),
                    ElevatedButton.icon(
                      onPressed: authStore.isLoading ? null : () => authStore.reloadUser(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Check Status'),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                TextButton(
                  onPressed: () => authStore.signOut(),
                  child: const Text('Sign Out'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
