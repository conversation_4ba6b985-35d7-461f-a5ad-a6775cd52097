import 'package:firebase_auth/firebase_auth.dart';
import '../models/auth_result.dart';
import '../models/user_model.dart';

/// Service class that wraps Firebase Authentication functionality
class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get current Firebase user
  User? get currentFirebaseUser => _auth.currentUser;

  /// Get current user as UserModel
  UserModel? get currentUser {
    final firebaseUser = currentFirebaseUser;
    return firebaseUser != null ? UserModel.fromFirebaseUser(firebaseUser) : null;
  }

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Stream of user changes (including profile updates)
  Stream<User?> get userChanges => _auth.userChanges();

  /// Sign in with email and password
  Future<AuthResult<UserModel>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        final userModel = UserModel.fromFirebaseUser(credential.user!);
        return AuthSuccess(userModel, message: 'Successfully signed in');
      } else {
        return const AuthFailure('Sign in failed: No user returned');
      }
    } on FirebaseAuthException catch (e) {
      return AuthFailure(_handleAuthException(e), code: e.code, exception: e);
    } catch (e) {
      return AuthFailure('An unexpected error occurred: $e', exception: Exception(e));
    }
  }

  /// Create user with email and password
  Future<AuthResult<UserModel>> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        final userModel = UserModel.fromFirebaseUser(credential.user!);
        return AuthSuccess(userModel, message: 'Account created successfully');
      } else {
        return const AuthFailure('Account creation failed: No user returned');
      }
    } on FirebaseAuthException catch (e) {
      return AuthFailure(_handleAuthException(e), code: e.code, exception: e);
    } catch (e) {
      return AuthFailure('An unexpected error occurred: $e', exception: Exception(e));
    }
  }

  /// Sign out current user
  Future<AuthResult<void>> signOut() async {
    try {
      await _auth.signOut();
      return const AuthSuccess(null, message: 'Successfully signed out');
    } catch (e) {
      return AuthFailure('Failed to sign out: $e', exception: Exception(e));
    }
  }

  /// Send password reset email
  Future<AuthResult<void>> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
      return const AuthSuccess(null, message: 'Password reset email sent');
    } on FirebaseAuthException catch (e) {
      return AuthFailure(_handleAuthException(e), code: e.code, exception: e);
    } catch (e) {
      return AuthFailure('Failed to send password reset email: $e', exception: Exception(e));
    }
  }

  /// Send email verification to current user
  Future<AuthResult<void>> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const AuthFailure('No user is currently signed in');
      }

      if (user.emailVerified) {
        return const AuthFailure('Email is already verified');
      }

      await user.sendEmailVerification();
      return const AuthSuccess(null, message: 'Verification email sent');
    } catch (e) {
      return AuthFailure('Failed to send email verification: $e', exception: Exception(e));
    }
  }

  /// Reload current user to get updated information
  Future<AuthResult<UserModel>> reloadUser() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const AuthFailure('No user is currently signed in');
      }

      await user.reload();
      final updatedUser = _auth.currentUser;
      if (updatedUser != null) {
        final userModel = UserModel.fromFirebaseUser(updatedUser);
        return AuthSuccess(userModel, message: 'User information updated');
      } else {
        return const AuthFailure('Failed to reload user information');
      }
    } catch (e) {
      return AuthFailure('Failed to reload user: $e', exception: Exception(e));
    }
  }

  /// Update user display name
  Future<AuthResult<UserModel>> updateDisplayName(String displayName) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const AuthFailure('No user is currently signed in');
      }

      await user.updateDisplayName(displayName.trim());
      await user.reload();
      
      final updatedUser = _auth.currentUser;
      if (updatedUser != null) {
        final userModel = UserModel.fromFirebaseUser(updatedUser);
        return AuthSuccess(userModel, message: 'Display name updated');
      } else {
        return const AuthFailure('Failed to update display name');
      }
    } catch (e) {
      return AuthFailure('Failed to update display name: $e', exception: Exception(e));
    }
  }

  /// Delete current user account
  Future<AuthResult<void>> deleteAccount() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const AuthFailure('No user is currently signed in');
      }

      await user.delete();
      return const AuthSuccess(null, message: 'Account deleted successfully');
    } on FirebaseAuthException catch (e) {
      return AuthFailure(_handleAuthException(e), code: e.code, exception: e);
    } catch (e) {
      return AuthFailure('Failed to delete account: $e', exception: Exception(e));
    }
  }

  /// Handle Firebase Auth exceptions and return user-friendly messages
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password is too weak.';
      case 'invalid-email':
        return 'The email address is invalid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'Email/password accounts are not enabled.';
      case 'invalid-credential':
        return 'The provided credentials are invalid.';
      case 'requires-recent-login':
        return 'This operation requires recent authentication. Please sign in again.';
      case 'network-request-failed':
        return 'Network error. Please check your connection and try again.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }
}
