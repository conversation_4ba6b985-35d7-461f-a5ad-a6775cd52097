import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/auth_result.dart';
import '../models/user_model.dart';
import 'auth_service.dart';

/// Repository interface for authentication operations
abstract class AuthRepository {
  /// Stream of authentication state changes
  Stream<UserModel?> get authStateChanges;

  /// Get current authenticated user
  UserModel? get currentUser;

  /// Sign in with email and password
  Future<AuthResult<UserModel>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Create user with email and password
  Future<AuthResult<UserModel>> createUserWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Sign out current user
  Future<AuthResult<void>> signOut();

  /// Send password reset email
  Future<AuthResult<void>> sendPasswordResetEmail({required String email});

  /// Send email verification
  Future<AuthResult<void>> sendEmailVerification();

  /// Reload user information
  Future<AuthResult<UserModel>> reloadUser();

  /// Update user display name
  Future<AuthResult<UserModel>> updateDisplayName(String displayName);

  /// Delete user account
  Future<AuthResult<void>> deleteAccount();
}

/// Implementation of AuthRepository using Firebase Auth
class FirebaseAuthRepository implements AuthRepository {
  final AuthService _authService;
  StreamController<UserModel?>? _authStateController;

  FirebaseAuthRepository(this._authService) {
    _initializeAuthStateStream();
  }

  void _initializeAuthStateStream() {
    _authStateController = StreamController<UserModel?>.broadcast();
    
    _authService.authStateChanges.listen((User? firebaseUser) {
      final userModel = firebaseUser != null 
          ? UserModel.fromFirebaseUser(firebaseUser) 
          : null;
      _authStateController?.add(userModel);
    });
  }

  @override
  Stream<UserModel?> get authStateChanges {
    return _authStateController?.stream ?? const Stream.empty();
  }

  @override
  UserModel? get currentUser => _authService.currentUser;

  @override
  Future<AuthResult<UserModel>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    return await _authService.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
  }

  @override
  Future<AuthResult<UserModel>> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    return await _authService.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
  }

  @override
  Future<AuthResult<void>> signOut() async {
    return await _authService.signOut();
  }

  @override
  Future<AuthResult<void>> sendPasswordResetEmail({
    required String email,
  }) async {
    return await _authService.sendPasswordResetEmail(email: email);
  }

  @override
  Future<AuthResult<void>> sendEmailVerification() async {
    return await _authService.sendEmailVerification();
  }

  @override
  Future<AuthResult<UserModel>> reloadUser() async {
    return await _authService.reloadUser();
  }

  @override
  Future<AuthResult<UserModel>> updateDisplayName(String displayName) async {
    return await _authService.updateDisplayName(displayName);
  }

  @override
  Future<AuthResult<void>> deleteAccount() async {
    return await _authService.deleteAccount();
  }

  /// Dispose resources
  void dispose() {
    _authStateController?.close();
  }
}
