/// Represents the different states of authentication
enum AuthState {
  /// User is not authenticated
  unauthenticated,
  
  /// User is authenticated
  authenticated,
  
  /// Authentication state is being determined
  loading,
  
  /// An error occurred during authentication
  error,
}

/// Extension to provide convenient methods for AuthState
extension AuthStateExtension on AuthState {
  /// Returns true if the user is authenticated
  bool get isAuthenticated => this == AuthState.authenticated;

  /// Returns true if the authentication state is loading
  bool get isLoading => this == AuthState.loading;

  /// Returns true if there's an authentication error
  bool get hasError => this == AuthState.error;

  /// Returns true if the user is not authenticated
  bool get isUnauthenticated => this == AuthState.unauthenticated;
}
