import 'package:get_it/get_it.dart';
import '../services/auth_service.dart';
import '../services/auth_repository.dart';
import '../stores/auth_store.dart';

/// Dependency injection setup for the auth package
class AuthDI {
  static final GetIt _getIt = GetIt.instance;

  /// Initialize all auth-related dependencies
  static Future<void> initialize() async {
    // Register AuthService as singleton
    _getIt.registerLazySingleton<AuthService>(() => AuthService());

    // Register AuthRepository as singleton
    _getIt.registerLazySingleton<AuthRepository>(
      () => FirebaseAuthRepository(_getIt<AuthService>()),
    );

    // Register AuthStore as singleton
    _getIt.registerLazySingleton<AuthStore>(
      () => AuthStore(_getIt<AuthRepository>()),
    );
  }

  /// Get AuthService instance
  static AuthService get authService => _getIt<AuthService>();

  /// Get AuthRepository instance
  static AuthRepository get authRepository => _getIt<AuthRepository>();

  /// Get AuthStore instance
  static AuthStore get authStore => _getIt<AuthStore>();

  /// Reset all registrations (useful for testing)
  static Future<void> reset() async {
    await _getIt.reset();
  }

  /// Dispose all resources
  static Future<void> dispose() async {
    // Dispose AuthStore if it exists
    if (_getIt.isRegistered<AuthStore>()) {
      _getIt<AuthStore>().dispose();
    }

    // Dispose AuthRepository if it exists and is FirebaseAuthRepository
    if (_getIt.isRegistered<AuthRepository>()) {
      final repository = _getIt<AuthRepository>();
      if (repository is FirebaseAuthRepository) {
        repository.dispose();
      }
    }

    await _getIt.reset();
  }

  /// Check if dependencies are initialized
  static bool get isInitialized => 
      _getIt.isRegistered<AuthService>() &&
      _getIt.isRegistered<AuthRepository>() &&
      _getIt.isRegistered<AuthStore>();
}

/// Extension to make GetIt usage more convenient
extension AuthGetItExtension on GetIt {
  /// Get AuthService instance
  AuthService get authService => get<AuthService>();

  /// Get AuthRepository instance
  AuthRepository get authRepository => get<AuthRepository>();

  /// Get AuthStore instance
  AuthStore get authStore => get<AuthStore>();
}
