import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../di/auth_di.dart';
import '../stores/auth_store.dart';

/// Route guard functions for go_router integration
class AuthRouteGuards {
  static AuthStore get _authStore => AuthDI.authStore;

  /// Redirect function for protecting authenticated routes
  /// Returns null if access is allowed, or a redirect path if not
  static String? requireAuth(BuildContext context, GoRouterState state) {
    final authStore = _authStore;

    // If still loading, don't redirect yet
    if (authStore.authState.isLoading) {
      return null;
    }

    // If not authenticated, redirect to sign in
    if (!authStore.isAuthenticated) {
      return '/auth/signin';
    }

    // User is authenticated, allow access
    return null;
  }

  /// Redirect function for protecting routes that require email verification
  static String? requireEmailVerification(BuildContext context, GoRouterState state) {
    final authStore = _authStore;

    // First check if user is authenticated
    final authRedirect = requireAuth(context, state);
    if (authRedirect != null) {
      return authRedirect;
    }

    // If authenticated but email not verified, redirect to verification screen
    if (authStore.isAuthenticated && !authStore.isEmailVerified) {
      return '/auth/verify-email';
    }

    // User is authenticated and email is verified
    return null;
  }

  /// Redirect function for guest-only routes (like sign in/up pages)
  /// Redirects authenticated users away from auth pages
  static String? requireGuest(BuildContext context, GoRouterState state) {
    final authStore = _authStore;

    // If still loading, don't redirect yet
    if (authStore.authState.isLoading) {
      return null;
    }

    // If authenticated, redirect to home
    if (authStore.isAuthenticated) {
      return '/home';
    }

    // User is not authenticated, allow access to auth pages
    return null;
  }

  /// Combined redirect function that handles all auth logic
  /// This is the main function to use with go_router
  static String? authRedirect(BuildContext context, GoRouterState state) {
    final authStore = _authStore;
    final location = state.matchedLocation;

    // If still loading, don't redirect yet
    if (authStore.authState.isLoading) {
      return null;
    }

    // Define route categories
    final isAuthRoute = location.startsWith('/auth');
    final isPublicRoute = _isPublicRoute(location);
    final requiresVerification = _requiresEmailVerification(location);

    // Handle unauthenticated users
    if (!authStore.isAuthenticated) {
      if (isPublicRoute || isAuthRoute) {
        return null; // Allow access to public and auth routes
      }
      return '/auth/signin'; // Redirect to sign in for protected routes
    }

    // Handle authenticated users
    if (authStore.isAuthenticated) {
      // Redirect away from auth routes
      if (isAuthRoute && location != '/auth/verify-email') {
        return '/home';
      }

      // Check email verification for routes that require it
      if (requiresVerification && !authStore.isEmailVerified) {
        return '/auth/verify-email';
      }

      // Allow access
      return null;
    }

    return null;
  }

  /// Check if a route is public (doesn't require authentication)
  static bool _isPublicRoute(String location) {
    final publicRoutes = [
      '/',
      '/about',
      '/privacy',
      '/terms',
      '/contact',
    ];
    return publicRoutes.contains(location);
  }

  /// Check if a route requires email verification
  static bool _requiresEmailVerification(String location) {
    final verificationRequiredRoutes = [
      '/profile',
      '/settings',
      '/admin',
    ];
    return verificationRequiredRoutes.any((route) => location.startsWith(route));
  }
}

/// Extension to make route guard usage more convenient
extension AuthRouteGuardExtension on GoRouter {
  /// Helper method to check if current route requires authentication
  bool get requiresAuth {
    final location = routerDelegate.currentConfiguration.uri.path;
    return AuthRouteGuards.authRedirect(
      GoRouterState(
        location: location,
        matchedLocation: location,
        name: null,
        path: location,
        fullPath: location,
        params: {},
        queryParams: {},
        extra: null,
        error: null,
        topRoute: null,
      ),
    ) != null;
  }
}
